import auth from '@react-native-firebase/auth';
import messaging from '@react-native-firebase/messaging';
import firestore from '@react-native-firebase/firestore';

export const FIREBASE_AUTH = auth();
export const FIREBASE_MESSAGING = messaging();
export const FIREBASE_DB = firestore();

const firebaseConfig = {
  apiKey: 'AIzaSyAHSwwzRKcwvyjaJjsI-JmpY8Oxz55tw7A',
  authDomain: 'prototype-reactnative-36322.firebaseapp.com',
  projectId: 'prototype-reactnative-36322',
  storageBucket: 'prototype-reactnative-36322.firebasestorage.app',
  messagingSenderId: '975670785554',
  appId: '1:975670785554:web:ec23d8519fd5c9792a4acb',
  measurementId: 'G-MDY3T27EKZ',
};
